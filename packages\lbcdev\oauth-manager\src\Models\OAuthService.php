<?php

namespace LBCDev\OAuthManager\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Encrypted;

class OAuthService extends Model
{
    protected $table = 'oauth_services';

    protected $fillable = [
        'name',
        'service_type',
        'credentials',
        'access_token',
        'refresh_token',
        'expires_at',
        'is_active',
        'last_used_at',
    ];

    protected $casts = [
        'credentials' => 'array',
        // 'access_token' => Encrypted::class,
        // 'refresh_token' => Encrypted::class,
        'expires_at' => 'datetime',
        'last_used_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    public function getRouteKeyName()
    {
        return 'slug';
    }

    public function getProviderInstance()
    {
        $serviceConfig = config("oauth-manager.services.{$this->service_type}");

        if (!$serviceConfig) {
            throw new \InvalidArgumentException("Service type {$this->service_type} not configured");
        }

        $providerClass = $serviceConfig['provider'];

        return new $providerClass($this);
    }

    public function isTokenExpired(): bool
    {
        return $this->expires_at && now()->isAfter($this->expires_at);
    }

    public function needsRefresh(): bool
    {
        return $this->isTokenExpired() && $this->refresh_token;
    }
}
