<?php

namespace LBC<PERSON>ev\OAuthManager\Console\Commands;

use Illuminate\Console\Command;
use <PERSON>BC<PERSON>ev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Services\OAuthManager;

class TestOAuthConnectionCommand extends Command
{
    protected $signature = 'oauth:test {service_id} {--refresh}';
    protected $description = 'Test OAuth connection for a service';

    public function handle(OAuthManager $oauthManager): void
    {
        $serviceId = $this->argument('service_id');
        $service = OAuthService::find($serviceId);

        if (!$service) {
            $this->error("Service with ID {$serviceId} not found");
            return;
        }

        $this->info("Testing connection for: {$service->name} ({$service->service_type})");

        // Test if service has token
        if (!$service->access_token) {
            $this->error('Service is not authorized. Please authorize it first.');
            return;
        }

        // Check if token is expired
        if ($service->isTokenExpired()) {
            $this->warn('Token is expired.');

            if ($this->option('refresh') && $service->refresh_token) {
                $this->info('Attempting to refresh token...');

                if ($oauthManager->refreshTokenIfNeeded($service)) {
                    $this->info('Token refreshed successfully!');
                } else {
                    $this->error('Failed to refresh token. Please re-authorize.');
                    return;
                }
            } else {
                $this->error('Token expired. Use --refresh flag or re-authorize.');
                return;
            }
        }

        // Test connection
        try {
            $provider = $service->getProviderInstance();

            if ($provider->testConnection()) {
                $this->info('✅ Connection test successful!');
            } else {
                $this->error('❌ Connection test failed');
            }
        } catch (\Exception $e) {
            $this->error('❌ Connection test failed: ' . $e->getMessage());
        }
    }
}
