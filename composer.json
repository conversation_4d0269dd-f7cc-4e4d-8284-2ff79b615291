{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/livewire-starter-kit", "type": "project", "description": "The official Laravel starter kit for Livewire.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "aliziodev/laravel-taxonomy": "^2.4", "filament/filament": "^3.3", "laravel/framework": "^12.0", "laravel/tinker": "^2.10.1", "lbcdev/oauth-manager": "@dev", "league/omnipay": "^3.2", "livewire/flux": "^2.1.1", "livewire/volt": "^1.7.0", "mcamara/laravel-localization": "^2.3", "omnipay/paypal": "^3.0", "omnipay/stripe": "^3.2", "spatie/laravel-permission": "^6.20"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pail": "^1.2.2", "laravel/pint": "^1.18", "laravel/sail": "^1.41", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "phpunit/phpunit": "^11.5.3"}, "repositories": [{"type": "path", "url": "packages/lbcdev/oauth-manager"}], "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"npm run dev\" --names='server,queue,vite'"], "test": ["@php artisan config:clear --ansi", "@php artisan test"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}