<?php

namespace LBC<PERSON>ev\OAuthManager;

use Livewire\Livewire;
use Illuminate\Support\ServiceProvider;
use LBCDev\OAuthManager\Services\OAuthManager;

class OAuthManagerServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $this->loadMigrationsFrom(__DIR__ . '/../database/migrations');
        $this->loadRoutesFrom(__DIR__ . '/../routes/web.php');

        $this->publishes([
            __DIR__ . '/../config/oauth-manager.php' => config_path('oauth-manager.php'),
        ], 'oauth-manager-config');

        $this->publishes([
            __DIR__ . '/../database/migrations/' => database_path('migrations'),
        ], 'oauth-manager-migrations');

        $this->publishes([
            __DIR__ . '/../database/seeders/' => database_path('seeders'),
        ], 'oauth-manager-seeders');

        $this->publishes([
            __DIR__ . '/../config/oauth-manager.php' => config_path('oauth-manager.php'),
            __DIR__ . '/../database/migrations/' => database_path('migrations'),
            __DIR__ . '/../database/seeders/' => database_path('seeders'),
        ], 'oauth-manager');

        if ($this->app->runningInConsole()) {
            $this->commands([
                \LBCDev\OAuthManager\Console\Commands\TestOAuthConnectionCommand::class,
                \LBCDev\OAuthManager\Console\Commands\RefreshTokenCommand::class,
            ]);
        }
    }

    public function register()
    {
        $this->mergeConfigFrom(__DIR__ . '/../config/oauth-manager.php', 'oauth-manager');

        $this->app->singleton(OAuthManager::class, function ($app) {
            return new OAuthManager();
        });
    }
}
