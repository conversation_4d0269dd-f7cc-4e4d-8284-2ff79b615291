{"name": "lbcdev/oauth-manager", "description": "Paquete para configurar servicios externos con OAuth", "autoload": {"psr-4": {"LBCDev\\OAuthManager\\": "src/"}}, "require": {"php": "^8.1", "laravel/framework": "^10.0|^11.0|^12.0", "league/oauth2-client": "^2.7", "league/oauth2-google": "^4.0", "google/apiclient": "^2.15"}, "scripts": {"pre-autoload-dump": "Google\\Task\\Composer::cleanup"}, "extra": {"laravel": {"providers": ["LBCDev\\OAuthManager\\OAuthManagerServiceProvider"]}, "google/apiclient-services": ["Drive", "YouTube"]}}