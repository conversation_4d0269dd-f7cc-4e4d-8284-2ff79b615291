<?php

use Illuminate\Support\Facades\Route;
use LBCDev\OAuthManager\Http\Controllers\OAuthController;

Route::get('/test-paquete', function () {
    return 'Paquete funcionando';
});

Route::middleware(config('oauth-manager.middleware'))
    ->prefix('oauth-manager')
    ->name('oauth-manager.')
    ->group(function () {
        Route::get('authorize/{service}', [OAuthController::class, 'authorize'])->name('authorize');
        Route::get('callback/{service}', [OAuthController::class, 'callback'])->name('callback');
    });
