<?php

namespace App\Filament\Resources;

use Dom\Text;
use Filament\Forms;
use App\Models\File;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\FileResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\FileResource\RelationManagers;
use App\Filament\Forms\Components\GoogleDriveExplorer;

class FileResource extends Resource
{
    protected static ?string $model = File::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = 'Cursos y materiales';

    protected static null|int $navigationSort = 5;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('nombre')->required(),

                Forms\Components\Select::make('oauth_service_id')
                    ->relationship('oauthService', 'name', fn($query) => $query->where('is_active', true)->where('access_token', '!=', null))
                    ->live()
                    ->required()
                    ->label('Google Drive Service')
                    ->helperText('Select the Google Drive service to browse files from'),

                GoogleDriveExplorer::make('url')
                    ->label('File URL')
                    ->oauthServiceField('oauth_service_id')
                    ->required()
                    ->helperText('Browse and select a file from Google Drive'),

                Forms\Components\Select::make('language_id')
                    ->relationship('language', 'name')
                    ->required(),
                Forms\Components\TextInput::make('orden')->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('nombre')->sortable(),
                TextColumn::make('url')->sortable(),
                TextColumn::make('language.name')->sortable(),
                TextColumn::make('orden')->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFiles::route('/'),
            'create' => Pages\CreateFile::route('/create'),
            'edit' => Pages\EditFile::route('/{record}/edit'),
        ];
    }
}
